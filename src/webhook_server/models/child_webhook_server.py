import argparse
import asyncio
import sys
from pathlib import Path

# 设置Python路径 - 必须在导入项目模块之前
if getattr(sys, 'frozen', False):
    # 打包后的环境
    src_path = str(Path(sys._MEIPASS))
else:
    # 开发环境，从当前文件位置推断
    src_path = str(Path(__file__).parent.parent.parent)

# 确保源代码路径在sys.path中
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from common.utils import path_manager  # 确保路径设置
from webhook_server.models import webhook_server


if __name__ == "__main__":
    """是直接给gui打开webhook server子进程的入口"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    asyncio.run(webhook_server.run_server_with_path(config_path=parser.parse_args().config, is_child_process=True))