"""统一的路径管理模块。

此模块提供了统一的路径处理功能，避免在多个文件中重复相同的路径处理逻辑。
"""

import sys
from pathlib import Path
from typing import Optional


class PathManager:
    """路径管理器，提供统一的路径处理功能。"""
    
    _src_path: Optional[str] = None
    _initialized: bool = False
    
    @classmethod
    def get_src_path(cls) -> str:
        """获取源代码根路径。
        
        支持打包后的环境和开发环境。
        
        Returns:
            str: 源代码根路径
        """
        if cls._src_path is None:
            # 动态路径处理，支持打包后的环境
            if getattr(sys, 'frozen', False):
                # 打包后的环境
                cls._src_path = str(Path(sys._MEIPASS))
            else:
                # 开发环境，从当前文件位置推断
                cls._src_path = str(Path(__file__).parent.parent.parent)
        
        return cls._src_path
    
    @classmethod
    def ensure_src_path_in_sys_path(cls) -> None:
        """确保源代码路径在sys.path中。
        
        这个方法是幂等的，可以安全地多次调用。
        """
        if not cls._initialized:
            src_path = cls.get_src_path()
            if src_path not in sys.path:
                sys.path.insert(0, src_path)
            cls._initialized = True
    
    @classmethod
    def get_relative_path(cls, *path_parts: str) -> str:
        """获取相对于源代码根目录的路径。
        
        Args:
            *path_parts: 路径组件
            
        Returns:
            str: 完整路径
        """
        src_path = cls.get_src_path()
        return str(Path(src_path).joinpath(*path_parts))


# 提供便捷的函数接口
def ensure_src_path() -> None:
    """确保源代码路径在sys.path中的便捷函数。"""
    PathManager.ensure_src_path_in_sys_path()


def get_src_path() -> str:
    """获取源代码根路径的便捷函数。"""
    return PathManager.get_src_path()