import argparse
import asyncio
import sys
from pathlib import Path


src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent) # noqa
if src_path not in sys.path:
    sys.path.append(src_path)

from models import webhook_server

if __name__ == "__main__":
    """是直接给gui打开webhook server子进程的入口"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--webhook_server.config", required=True, help="path to server webhook_server.config file")
    asyncio.run(webhook_server.run_server_with_path(config_path=parser.parse_args().config,is_child_process=True))
